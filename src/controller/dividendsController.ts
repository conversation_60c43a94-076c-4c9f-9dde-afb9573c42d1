import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {ApiEOD} from "../providers/implements/ApiEOD";
import {iDividendsEODHD} from "../repositories/iDividendsEODHD";
import {DividendsEODHDAO} from "../repositories/implements/DIvidendsEODHDDAO";
import {HistoryDividendsEODRepository} from "../repositories/implements/HistoryDividendsEODRepository";
import {LogLevel} from "@/utils/types/logs/log";
import {addLogJobExecution} from "@/lib/agenda";

export async function getDividendsController(updatedTickers: ListOfTickers[], historyId?: string): Promise<void> {
    try {
        addLogJobExecution(LogLevel.INFO, "getDividendsController", "Retrieving dividends for tickers", {tickersLength: updatedTickers.length});

        // Initialize progress tracking if historyId is provided
        if (historyId) {
            const {initializeJobProgress} = await import("../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await initializeJobProgress(new ObjectId(historyId), updatedTickers.length);
            } catch (error) {
                console.error("Error initializing job progress:", error);
            }
        }

        for (let i = 0; i < updatedTickers.length; i++) {
            try {
                console.log(`[${i + 1}/${updatedTickers.length}] Processing ticker: ${updatedTickers[i].primary_ticker_eodhd} (ID: ${updatedTickers[i].id})`);
                const api = new ApiEOD();
                const response: iDividendsEODHD[] = await api.findDividends(updatedTickers[i].primary_ticker_eodhd);
                if (response?.length > 0) {
                    const dividends: DividendsEODHDAO[] = [];

                    for (let j = 0; j < response.length; j++) {
                        const dividend: iDividendsEODHD = {...response[j]};
                        const div = new DividendsEODHDAO(dividend, updatedTickers[i].id || 0);
                        dividends.push(div);
                    }
                    await insertDividends(dividends, updatedTickers[i].id || 0);
                }
            } catch (err) {
                addLogJobExecution(LogLevel.ERROR, "getDividendsController", "Error when try to get dividends", {error: err instanceof Error ? err.message : String(err)}, updatedTickers[i].id);
                continue;
            }

            // Update progress tracking if historyId is provided
            if (historyId) {
                const {updateJobProgress} = await import("../lib/jobDefinitions");
                const {ObjectId} = await import("mongodb");
                try {
                    await updateJobProgress(new ObjectId(historyId), i + 1);
                } catch (error) {
                    console.error("Error updating job progress:", error);
                }
            }
        }
        addLogJobExecution(LogLevel.INFO, "getDividendsController", "Retrieved dividends for tickers", {tickersLength: updatedTickers.length});
    } catch (error) {
        addLogJobExecution(LogLevel.ERROR, "getDividendsController", "Error when try to get dividends", {error: error instanceof Error ? error.message : String(error)});
        console.log(error);
    }
}

export async function insertDividends(dividends: DividendsEODHDAO[], ticker_internal_id: number) {
    addLogJobExecution(LogLevel.INFO, "insertDividends", "Inserting dividends for ticker", {dividendsLength: dividends.length}, ticker_internal_id);
    const dividendsRepository = new HistoryDividendsEODRepository(ticker_internal_id, dividends);
    await dividendsRepository.getSavedDividends();
    const toSave = await dividendsRepository.getDividends();
    if (toSave.length > 0) {
        addLogJobExecution(LogLevel.INFO, "insertDividends", "Saving dividends for ticker", {toSaveLength: toSave.length}, ticker_internal_id);
        await dividendsRepository.saveDividends(toSave);
        addLogJobExecution(LogLevel.INFO, "insertDividends", "Inserted dividends for ticker", {ticker_internal_id}, ticker_internal_id);
    } else {
        addLogJobExecution(LogLevel.INFO, "insertDividends", "No dividends to save for ticker", {}, ticker_internal_id);
    }
}
